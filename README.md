# OA Excel 下载器优化版

## 📋 概述

这是一个用于从雅朵OA系统下载Excel报表的Python工具，经过全面优化，具有更好的错误处理、日志记录和配置管理功能。

## ✨ 主要改进

### 🔧 代码结构优化
- **类型注解**: 添加了完整的类型提示，提高代码可读性和IDE支持
- **数据类**: 使用`@dataclass`定义配置和数据结构
- **枚举类**: 使用`Enum`定义导出状态常量
- **异常处理**: 完善的异常捕获和处理机制

### 📝 日志系统
- **结构化日志**: 使用Python标准logging模块
- **多级日志**: INFO、DEBUG、WARNING、ERROR等级别
- **文件日志**: 自动保存到`oa_downloader.log`文件
- **控制台输出**: 同时在控制台显示日志信息

### ⚙️ 配置管理
- **配置类**: `OAConfig`数据类管理所有配置项
- **环境变量**: 支持从环境变量读取配置
- **JSON配置**: 支持从`config.json`文件读取配置
- **默认配置**: 内置合理的默认值

### 🛡️ 错误处理
- **网络异常**: 处理requests相关的网络错误
- **JSON解析**: 处理响应数据解析错误
- **文件操作**: 处理文件读写相关错误
- **超时控制**: 可配置的请求超时和重试机制

## 🚀 使用方法

### 基本使用

```python
from OA import OAExcelDownloader

# 使用默认配置
downloader = OAExcelDownloader()

# 下载报表
success = downloader.full_download_process(
    report_id=216,
    filename="预付款申请单",
    output_dir="./downloads"
)
```

### 自定义配置

```python
from OA import OAExcelDownloader, OAConfig

# 创建自定义配置
config = OAConfig(
    base_url="http://oa.yaduo.com",
    timeout=60,
    max_retries=50,
    retry_interval=3,
    cookies={
        'ecology_JSessionid': 'your_session_id',
        'loginidweaver': 'your_username',
        # 其他cookies...
    }
)

# 使用自定义配置创建下载器
downloader = OAExcelDownloader(config=config)
```

### 使用配置文件

```python
import json
from OA import OAExcelDownloader, OAConfig

# 从JSON文件加载配置
with open('config.json', 'r', encoding='utf-8') as f:
    config_data = json.load(f)

config = OAConfig(**config_data)
downloader = OAExcelDownloader(config=config)
```

### 环境变量配置

```bash
# 设置环境变量
export OA_BASE_URL="http://oa.yaduo.com"
export OA_TIMEOUT="60"
export OA_MAX_RETRIES="50"
export OA_RETRY_INTERVAL="3"
export OA_COOKIES='{"ecology_JSessionid": "your_session_id"}'
```

## 📁 文件结构

```
OA报表/
├── OA.py              # 主程序文件（优化版）
├── config.json        # 配置文件示例
├── README.md          # 使用说明
├── mock/              # 请求响应示例
│   ├── [36] request_oa.yaduo.com_message.txt
│   ├── [36] response_oa.yaduo.com_message.txt
│   └── ...
├── downloads/         # 下载文件目录（自动创建）
└── oa_downloader.log  # 日志文件（自动创建）
```

## 🔍 API方法说明

### 核心方法

- `get_report_data()`: 获取报表数据，返回sessionkey
- `export_excel()`: 请求异步导出Excel文件
- `check_export_status()`: 检查导出任务状态
- `download_file()`: 下载生成的Excel文件
- `full_download_process()`: 完整的下载流程

### 辅助方法

- `get_table_data()`: 获取表格数据详情
- `get_table_count()`: 获取表格数据总数
- `update_export_status()`: 更新导出任务状态

## 📊 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_url` | str | "http://oa.yaduo.com" | OA系统基础URL |
| `timeout` | int | 30 | 请求超时时间（秒） |
| `max_retries` | int | 30 | 最大重试次数 |
| `retry_interval` | int | 2 | 重试间隔时间（秒） |
| `cookies` | Dict | - | 认证cookies |
| `headers` | Dict | - | 自定义请求头 |

## 🐛 错误排查

### 常见问题

1. **认证失败**: 检查cookies是否过期
2. **网络超时**: 增加timeout配置值
3. **下载失败**: 检查文件权限和磁盘空间
4. **导出超时**: 增加max_retries配置值

### 日志分析

查看`oa_downloader.log`文件获取详细的错误信息：

```bash
tail -f oa_downloader.log
```

## 🔄 版本对比

| 功能 | 原版本 | 优化版本 |
|------|--------|----------|
| 类型注解 | ❌ | ✅ |
| 日志记录 | 简单print | 完整logging |
| 错误处理 | 基础 | 全面异常处理 |
| 配置管理 | 硬编码 | 灵活配置系统 |
| 文档说明 | 简单注释 | 详细文档 |
| 代码结构 | 单一类 | 模块化设计 |

## 📈 性能优化

- **连接复用**: 使用Session对象复用HTTP连接
- **流式下载**: 大文件分块下载，节省内存
- **智能重试**: 可配置的重试机制
- **超时控制**: 避免长时间等待

## 🛠️ 开发建议

1. **定期更新cookies**: 建议定期更新认证信息
2. **监控日志**: 定期检查日志文件，及时发现问题
3. **备份配置**: 保存好配置文件，便于部署
4. **测试环境**: 在测试环境验证后再用于生产

## 📞 技术支持

如有问题，请查看日志文件或联系技术支持团队。
