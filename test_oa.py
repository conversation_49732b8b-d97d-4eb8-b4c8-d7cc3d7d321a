#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA Excel下载器测试脚本

用于测试OA.py的各项功能
"""

import json
import logging
from pathlib import Path
from OA import OAExcelDownloader, OAConfig, ExportTaskInfo

# 设置测试日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def test_config_creation():
    """测试配置创建"""
    print("=== 测试配置创建 ===")
    
    # 测试默认配置
    config1 = OAConfig()
    print(f"默认配置: {config1}")
    
    # 测试自定义配置
    config2 = OAConfig(
        base_url="http://test.oa.com",
        timeout=60,
        max_retries=50
    )
    print(f"自定义配置: {config2}")
    
    print("✅ 配置创建测试通过\n")


def test_downloader_initialization():
    """测试下载器初始化"""
    print("=== 测试下载器初始化 ===")
    
    # 测试默认初始化
    downloader1 = OAExcelDownloader()
    print(f"默认下载器基础URL: {downloader1.config.base_url}")
    
    # 测试自定义配置初始化
    custom_config = OAConfig(
        base_url="http://test.oa.com",
        timeout=60
    )
    downloader2 = OAExcelDownloader(config=custom_config)
    print(f"自定义下载器基础URL: {downloader2.config.base_url}")
    
    print("✅ 下载器初始化测试通过\n")


def test_export_task_info():
    """测试导出任务信息数据类"""
    print("=== 测试导出任务信息 ===")
    
    task_info = ExportTaskInfo(
        status=1,
        percent="100",
        task_id=12345,
        file_id="test_file_id",
        completed=True,
        message="导出完成"
    )
    
    print(f"任务信息: {task_info}")
    print(f"是否完成: {task_info.completed}")
    print(f"进度: {task_info.percent}%")
    
    print("✅ 导出任务信息测试通过\n")


def test_config_from_json():
    """测试从JSON文件加载配置"""
    print("=== 测试JSON配置加载 ===")
    
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 移除不属于OAConfig的字段
            oa_config_fields = {'base_url', 'cookies', 'headers', 'timeout', 'max_retries', 'retry_interval'}
            filtered_config = {k: v for k, v in config_data.items() if k in oa_config_fields}
            
            config = OAConfig(**filtered_config)
            print(f"从JSON加载的配置: {config}")
            print("✅ JSON配置加载测试通过")
        except Exception as e:
            print(f"❌ JSON配置加载失败: {e}")
    else:
        print("⚠️ config.json文件不存在，跳过测试")
    
    print()


def test_mock_api_calls():
    """测试模拟API调用（不实际发送请求）"""
    print("=== 测试模拟API调用 ===")
    
    downloader = OAExcelDownloader()
    
    # 测试URL构建
    base_url = downloader.config.base_url
    
    urls = {
        "报表数据": f"{base_url}/api/workflow/standCustomReport/getReportData",
        "导出Excel": f"{base_url}/api/workflow/standCustomReport/exportExcel",
        "检查状态": f"{base_url}/api/workflow/standCustomReport/exportTask",
        "下载文件": f"{base_url}/weaver/weaver.file.FileDownload",
        "表格数据": f"{base_url}/api/ec/dev/table/datas",
        "数据总数": f"{base_url}/api/ec/dev/table/counts"
    }
    
    for name, url in urls.items():
        print(f"{name}: {url}")
    
    print("✅ URL构建测试通过\n")


def test_file_operations():
    """测试文件操作"""
    print("=== 测试文件操作 ===")
    
    # 测试目录创建
    test_dir = Path("./test_downloads")
    test_dir.mkdir(exist_ok=True)
    print(f"测试目录创建: {test_dir}")
    
    # 测试文件路径生成
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_file = test_dir / f"test_report-{timestamp}.xlsx"
    print(f"测试文件路径: {test_file}")
    
    # 清理测试目录
    if test_dir.exists() and test_dir.is_dir():
        try:
            test_dir.rmdir()
            print("测试目录已清理")
        except OSError:
            print("测试目录非空，保留")
    
    print("✅ 文件操作测试通过\n")


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行OA下载器测试套件\n")
    
    try:
        test_config_creation()
        test_downloader_initialization()
        test_export_task_info()
        test_config_from_json()
        test_mock_api_calls()
        test_file_operations()
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logger.exception("详细错误信息:")


if __name__ == "__main__":
    run_all_tests()
