import requests
import time
import json
import urllib.parse
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('oa_downloader.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ExportStatus(Enum):
    """导出状态枚举"""
    PENDING = 0
    COMPLETED = 1
    FAILED = 2

@dataclass
class ExportTaskInfo:
    """导出任务信息"""
    status: int
    percent: str
    task_id: Optional[int]
    file_id: Optional[str]
    completed: bool
    message: Optional[str] = None

@dataclass
class OAConfig:
    """OA配置信息"""
    base_url: str = "http://oa.yaduo.com"
    cookies: Optional[Dict[str, str]] = None
    headers: Optional[Dict[str, str]] = None
    timeout: int = 30
    max_retries: int = 30
    retry_interval: int = 2

class OAExcelDownloader:
    def __init__(self, config: Optional[OAConfig] = None):
        """
        初始化OA Excel下载器

        Args:
            config: OA配置信息，如果为None则使用默认配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._load_default_config()
        self.session = requests.Session()

        # 设置会话超时
        self.session.timeout = self.config.timeout

        # 设置默认headers
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'Referer': f'{self.config.base_url}/spa/workflow/static/index.html',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'X-Requested-With': 'XMLHttpRequest'
        }

        # 合并用户自定义headers
        if self.config.headers:
            default_headers.update(self.config.headers)

        self.session.headers.update(default_headers)

        # 设置cookies
        if self.config.cookies:
            self.session.cookies.update(self.config.cookies)

        self.logger.info(f"OA下载器初始化完成，基础URL: {self.config.base_url}")

    def _load_default_config(self) -> OAConfig:
        """加载默认配置"""
        default_cookies = {
            'ecology_JSessionid': 'aaappImyV2dlTf859-RBz',
            'Systemlanguid': '7',
            'languageidweaver': '7',
            'JSESSIONID': 'aaappImyV2dlTf859-RBz',
            'loginuuids': '34712',
            'asso_token_change_flag': '2026cf459faadacc526798043e6cedc5',
            'acw_tc': '0a45644e17489191867488719e68a777bb42854291f591959e7a102ea278b2',
            'loginidweaver': 'hui.zhang1',
            '__randcode__': '5b6f1d57-b88e-4838-a586-9719ebbcc075'
        }

        return OAConfig(cookies=default_cookies)

    def get_report_data(self, report_id: int = 216, field_conditions: Optional[Dict[str, str]] = None) -> Optional[str]:
        """
        步骤1: 获取报表数据，设置筛选条件

        Args:
            report_id: 报表ID
            field_conditions: 筛选条件字典

        Returns:
            sessionkey字符串，失败返回None
        """
        url = f"{self.config.base_url}/api/workflow/standCustomReport/getReportData"

        # 默认的筛选条件
        if field_conditions is None:
            field_conditions = {
                'field-10_opt1': '1',
                'field-10_value1': '547736',
                'field-12_value1': '1107',
                'conditionfieldids': '-10,-12',
                'templateid': '0',
                'reportParamsKey': f'{report_id}{int(time.time() * 1000)}',
                'reportid': str(report_id),
                'tabKey': '0'
            }

        try:
            self.logger.info(f"正在获取报表数据，报表ID: {report_id}")
            response = self.session.post(url, data=field_conditions)
            self.logger.info(f"获取报表数据响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                self.logger.debug(f"服务器响应内容: {result}")

                # sessionkey 直接在 result 中
                if 'sessionkey' in result:
                    session_key = result['sessionkey']
                    self.logger.info(f"成功获取sessionkey: {session_key}")
                    return session_key
                else:
                    self.logger.warning("响应中未找到sessionkey")
                    return None
            else:
                self.logger.error(f"获取报表数据失败，状态码: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"请求报表数据时发生网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"解析响应JSON时发生错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取报表数据时发生未知错误: {e}")
            return None

    def export_excel(self, report_id: int = 216, data_key: Optional[str] = None, filename: str = "预付款申请单（2023）") -> bool:
        """
        步骤2: 导出Excel文件（异步）

        Args:
            report_id: 报表ID
            data_key: 数据键，如果为None则使用默认值
            filename: 导出文件名

        Returns:
            是否成功启动导出任务
        """
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportExcel"

        # 如果没有提供data_key，使用从curl中提取的示例
        if data_key is None:
            data_key = "b5160681-0e9e-40f0-908a-fab5d3be0d3b216_0_B589092E18923CF5641B561B7FA669B3"
            self.logger.warning("未提供data_key，使用默认值")

        params = {
            'tableKey': data_key,
            'sortFields': 'field19514',
            'sumMainFields': '',
            'reportid': str(report_id),
            'filename': filename,
            '__random__': str(int(time.time() * 1000))
        }

        try:
            self.logger.info(f"正在请求导出Excel，报表ID: {report_id}, 文件名: {filename}")
            response = self.session.get(url, params=params)
            self.logger.info(f"导出Excel请求响应状态: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.logger.debug(f"导出响应: {result}")

                    # 根据实际响应，会返回 {"code":1,"data":"异步导出"}
                    if result.get('code') == 1 and result.get('data') == '异步导出':
                        self.logger.info("异步导出任务启动成功")
                        return True
                    else:
                        self.logger.warning(f"导出任务启动失败，响应: {result}")
                        return False

                except json.JSONDecodeError as e:
                    self.logger.error(f"解析导出响应JSON时发生错误: {e}")
                    return False
            else:
                self.logger.error(f"导出Excel请求失败，状态码: {response.status_code}")
                return False

        except requests.RequestException as e:
            self.logger.error(f"请求导出Excel时发生网络错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"导出Excel时发生未知错误: {e}")
            return False

    def check_export_status(self, report_id: int = 216) -> Optional[ExportTaskInfo]:
        """
        步骤3: 检查导出任务状态

        Args:
            report_id: 报表ID

        Returns:
            导出任务信息，失败返回None
        """
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportTask"

        params = {
            'reportid': str(report_id),
            'method': 'list',
            '__random__': str(int(time.time() * 1000))
        }

        try:
            response = self.session.get(url, params=params)
            self.logger.debug(f"检查导出状态响应: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('status') and result.get('datas'):
                        # 获取最新的导出任务
                        task_data = result['datas'][0] if result['datas'] else None
                        if task_data:
                            status = task_data.get('status', 0)  # 0=导出中, 1=完成
                            percent = task_data.get('exportpercent', '0')
                            task_id = task_data.get('id')
                            file_id = task_data.get('fileid', '')
                            status_msg = task_data.get('statuscol', '')

                            self.logger.info(f"导出进度: {percent}% - {status_msg}")

                            return ExportTaskInfo(
                                status=status,
                                percent=percent,
                                task_id=task_id,
                                file_id=file_id,
                                completed=status == 1,
                                message=status_msg
                            )
                    else:
                        self.logger.warning("导出状态响应中未找到有效数据")
                        return None

                except json.JSONDecodeError as e:
                    self.logger.error(f"解析导出状态响应JSON时发生错误: {e}")
                    return None
            else:
                self.logger.error(f"检查导出状态失败，状态码: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"请求导出状态时发生网络错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"检查导出状态时发生未知错误: {e}")
            return None

    def update_export_status(self, report_id: int = 216, task_id: int = 16808, status: int = 2) -> bool:
        """
        步骤4: 更新导出任务状态

        Args:
            report_id: 报表ID
            task_id: 任务ID
            status: 状态值

        Returns:
            是否更新成功
        """
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportTask"

        params = {
            'reportid': str(report_id),
            'method': 'update',
            'id': str(task_id),
            'status': str(status),
            '__random__': str(int(time.time() * 1000))
        }

        try:
            self.logger.info(f"正在更新任务状态，任务ID: {task_id}, 状态: {status}")
            response = self.session.get(url, params=params)
            self.logger.info(f"更新任务状态响应: {response.status_code}")

            success = response.status_code == 200
            if success:
                self.logger.info("任务状态更新成功")
            else:
                self.logger.error(f"任务状态更新失败，状态码: {response.status_code}")
            return success

        except requests.RequestException as e:
            self.logger.error(f"更新任务状态时发生网络错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"更新任务状态时发生未知错误: {e}")
            return False

    def download_file(self, file_id: str, output_path: str = "downloaded_report.xlsx") -> bool:
        """
        步骤5: 下载生成的Excel文件

        Args:
            file_id: 文件ID
            output_path: 输出文件路径

        Returns:
            是否下载成功
        """
        url = f"{self.config.base_url}/weaver/weaver.file.FileDownload"

        params = {
            'fileid': file_id
        }

        # 下载时需要不同的headers
        download_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Upgrade-Insecure-Requests': '1'
        }

        try:
            self.logger.info(f"正在下载文件，文件ID: {file_id}")
            response = self.session.get(url, params=params, headers=download_headers, stream=True)
            self.logger.info(f"下载文件响应状态: {response.status_code}")

            if response.status_code == 200:
                # 确保输出目录存在
                output_dir = Path(output_path).parent
                output_dir.mkdir(parents=True, exist_ok=True)

                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:  # 过滤掉保持连接的空块
                            f.write(chunk)

                file_size = Path(output_path).stat().st_size
                self.logger.info(f"文件下载成功，保存到: {output_path}, 大小: {file_size} bytes")
                return True
            else:
                self.logger.error(f"文件下载失败，状态码: {response.status_code}")
                return False

        except requests.RequestException as e:
            self.logger.error(f"下载文件时发生网络错误: {e}")
            return False
        except IOError as e:
            self.logger.error(f"保存文件时发生IO错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"下载文件时发生未知错误: {e}")
            return False

    def get_table_data(self, data_key: str, current_page: int = 1) -> Optional[Dict]:
        """
        获取表格数据详情

        Args:
            data_key: 数据键
            current_page: 当前页码

        Returns:
            表格数据字典，失败返回None
        """
        url = f"{self.config.base_url}/api/ec/dev/table/datas"

        data = {
            'dataKey': data_key,
            'current': str(current_page),
            'sortParams': '[]'
        }

        try:
            self.logger.info(f"正在获取表格数据，页码: {current_page}")
            response = self.session.post(url, data=data)
            self.logger.info(f"获取表格数据响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                self.logger.debug(f"表格数据获取成功")
                return result
            else:
                self.logger.error(f"获取表格数据失败，状态码: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"获取表格数据时发生网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"解析表格数据响应JSON时发生错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取表格数据时发生未知错误: {e}")
            return None

    def get_table_count(self, data_key: str) -> Optional[Dict]:
        """
        获取表格数据总数

        Args:
            data_key: 数据键

        Returns:
            数据总数字典，失败返回None
        """
        url = f"{self.config.base_url}/api/ec/dev/table/counts"

        data = {
            'dataKey': data_key
        }

        try:
            self.logger.info("正在获取表格数据总数")
            response = self.session.post(url, data=data)
            self.logger.info(f"获取数据总数响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                self.logger.debug("数据总数获取成功")
                return result
            else:
                self.logger.error(f"获取数据总数失败，状态码: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"获取数据总数时发生网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"解析数据总数响应JSON时发生错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"获取数据总数时发生未知错误: {e}")
            return None

    def full_download_process(self,
                             report_id: int = 216,
                             field_conditions: Optional[Dict[str, str]] = None,
                             filename: str = "预付款申请单（2023）",
                             output_dir: str = "./downloads") -> bool:
        """
        完整的下载流程

        Args:
            report_id: 报表ID
            field_conditions: 筛选条件字典
            filename: 文件名（不含扩展名）
            output_dir: 输出目录

        Returns:
            是否下载成功
        """
        self.logger.info("=== 开始Excel报表下载流程 ===")

        try:
            # 步骤1: 获取报表数据
            self.logger.info("步骤1: 获取报表数据...")
            data_key = self.get_report_data(report_id, field_conditions)
            if not data_key:
                self.logger.error("❌ 获取报表数据失败")
                return False

            self.logger.info(f"✅ 获取到数据键: {data_key}")

            # 步骤2: 请求导出Excel（异步）
            self.logger.info("步骤2: 请求导出Excel...")
            export_success = self.export_excel(report_id, data_key, filename)
            if not export_success:
                self.logger.error("❌ 导出请求失败")
                return False

            self.logger.info("✅ 异步导出任务已启动")

            # 步骤3: 轮询检查导出状态
            self.logger.info("步骤3: 等待导出完成...")
            task_id = None
            file_id = None

            for i in range(self.config.max_retries):
                status_info = self.check_export_status(report_id)

                if status_info:
                    if status_info.completed:
                        file_id = status_info.file_id
                        task_id = status_info.task_id
                        self.logger.info(f"✅ 导出完成！文件ID: {file_id}")
                        break
                    else:
                        self.logger.info(f"⏳ 导出进度: {status_info.percent}%")
                else:
                    self.logger.warning("⚠️ 无法获取导出状态")

                if i < self.config.max_retries - 1:  # 最后一次不要等待
                    time.sleep(self.config.retry_interval)
            else:
                self.logger.error("❌ 导出超时")
                return False

            # 步骤4: 更新任务状态（可选）
            if task_id:
                self.logger.info("步骤4: 更新任务状态...")
                self.update_export_status(report_id, task_id, 2)

            # 步骤5: 下载文件
            if file_id:
                self.logger.info("步骤5: 下载文件...")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = Path(output_dir) / f"{filename}-{timestamp}.xlsx"

                if self.download_file(file_id, str(output_path)):
                    self.logger.info(f"✅ 下载完成: {output_path}")
                    return True
                else:
                    self.logger.error("❌ 文件下载失败")
                    return False
            else:
                self.logger.error("❌ 未获取到文件ID")
                return False

        except Exception as e:
            self.logger.error(f"下载流程中发生未知错误: {e}")
            return False

    def _extract_file_id(self, status_result: Dict) -> Optional[str]:
        """
        从状态响应中提取文件ID（已弃用，现在直接在check_export_status中处理）

        Args:
            status_result: 状态响应字典

        Returns:
            文件ID，失败返回None
        """
        if isinstance(status_result, dict):
            # 检查各种可能的字段名
            for key in ['fileId', 'fileid', 'file_id', 'downloadUrl', 'url']:
                if key in status_result:
                    return status_result[key]
        return None

    def create_config_from_env(self) -> OAConfig:
        """
        从环境变量创建配置

        Returns:
            OA配置对象
        """
        import os

        config = OAConfig()

        # 从环境变量读取配置
        config.base_url = os.getenv('OA_BASE_URL', config.base_url)
        config.timeout = int(os.getenv('OA_TIMEOUT', str(config.timeout)))
        config.max_retries = int(os.getenv('OA_MAX_RETRIES', str(config.max_retries)))
        config.retry_interval = int(os.getenv('OA_RETRY_INTERVAL', str(config.retry_interval)))

        # 从环境变量读取cookies（JSON格式）
        cookies_env = os.getenv('OA_COOKIES')
        if cookies_env:
            try:
                config.cookies = json.loads(cookies_env)
            except json.JSONDecodeError:
                self.logger.warning("环境变量OA_COOKIES格式错误，使用默认cookies")

        return config


def create_downloader_with_custom_config() -> OAExcelDownloader:
    """
    创建带有自定义配置的下载器示例

    Returns:
        配置好的下载器实例
    """
    # 自定义配置示例
    custom_config = OAConfig(
        base_url="http://oa.yaduo.com",
        timeout=60,
        max_retries=50,
        retry_interval=3,
        cookies={
            'ecology_JSessionid': 'your_session_id',
            'loginidweaver': 'your_username',
            # 其他必要的cookies...
        }
    )

    return OAExcelDownloader(config=custom_config)


# 使用示例
if __name__ == "__main__":
    # 使用默认配置
    downloader = OAExcelDownloader()

    # 或者使用自定义配置
    # downloader = create_downloader_with_custom_config()

    # 复现 cURL 中 "未设置条件" 时的筛选逻辑
    custom_conditions_from_curl = {
        'field-12_value1': '1107',
        'conditionfieldids': '-12',
        'templateid': '0',
    }

    # 简化的筛选条件
    minimal_custom_conditions = {
        'field-12_value1': '1107',
        'conditionfieldids': '-12',
        'templateid': '0'
    }

    try:
        success = downloader.full_download_process(
            field_conditions=minimal_custom_conditions,
            filename="预付款申请单",
            output_dir="./downloads"
        )

        if success:
            print("✅ 下载流程完成")
        else:
            print("❌ 下载流程失败")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断下载流程")
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        logging.exception("详细错误信息:")