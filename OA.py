import requests
import time
import json
import urllib.parse
from pathlib import Path

class OAExcelDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://oa.yaduo.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'Referer': 'http://oa.yaduo.com/spa/workflow/static/index.html',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 从curl中提取的Cookie
        self.cookies = {
            'ecology_JSessionid': 'aaappImyV2dlTf859-RBz',
            'Systemlanguid': '7',
            'languageidweaver': '7',
            'JSESSIONID': 'aaappImyV2dlTf859-RBz',
            'loginuuids': '34712',
            'asso_token_change_flag': '2026cf459faadacc526798043e6cedc5',
            'acw_tc': '0a45644e17489191867488719e68a777bb42854291f591959e7a102ea278b2',
            'loginidweaver': 'hui.zhang1',
            '__randcode__': '5b6f1d57-b88e-4838-a586-9719ebbcc075'
        }
        
        self.session.cookies.update(self.cookies)
        self.session.headers.update(self.headers)

    def get_report_data(self, report_id=216, field_conditions=None):
        """
        步骤1: 获取报表数据，设置筛选条件
        """
        url = f"{self.base_url}/api/workflow/standCustomReport/getReportData"
        
        # 默认的筛选条件（从curl中提取）
        if field_conditions is None:
            field_conditions = {
                'field-10_opt1': '1',
                'field-10_value1': '547736',
                'field-12_value1': '1107',
                'conditionfieldids': '-10,-12',
                'templateid': '0',
                'reportParamsKey': f'{report_id}{int(time.time() * 1000)}',
                'reportid': str(report_id),
                'tabKey': '0'
            }
        
        response = self.session.post(url, data=field_conditions)
        print(f"获取报表数据: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"服务器响应内容: {result}")
            # sessionkey 直接在 result 中
            if 'sessionkey' in result:
                return result['sessionkey']
            return None
        return None

    def export_excel(self, report_id=216, data_key=None, filename="预付款申请单（2023）"):
        """
        步骤2: 导出Excel文件（异步）
        """
        url = f"{self.base_url}/api/workflow/standCustomReport/exportExcel"
        
        # 如果没有提供data_key，使用从curl中提取的示例
        if data_key is None:
            data_key = "b5160681-0e9e-40f0-908a-fab5d3be0d3b216_0_B589092E18923CF5641B561B7FA669B3"
        
        params = {
            'tableKey': data_key,
            'sortFields': 'field19514',
            'sumMainFields': '',
            'reportid': str(report_id),
            'filename': filename,
            '__random__': str(int(time.time() * 1000))
        }
        
        response = self.session.get(url, params=params)
        print(f"导出Excel请求: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("导出响应:", result)
                # 根据实际响应，会返回 {"code":1,"data":"异步导出"}
                if result.get('code') == 1 and result.get('data') == '异步导出':
                    return True
                return False
            except:
                print("导出响应解析失败")
                return False
        return False

    def check_export_status(self, report_id=216):
        """
        步骤3: 检查导出任务状态
        """
        url = f"{self.base_url}/api/workflow/standCustomReport/exportTask"
        
        params = {
            'reportid': str(report_id),
            'method': 'list',
            '__random__': str(int(time.time() * 1000))
        }
        
        response = self.session.get(url, params=params)
        print(f"检查导出状态: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('status') and result.get('datas'):
                    # 获取最新的导出任务
                    task_data = result['datas'][0] if result['datas'] else None
                    if task_data:
                        status = task_data.get('status', 0)  # 0=导出中, 1=完成
                        percent = task_data.get('exportpercent', '0')
                        task_id = task_data.get('id')
                        file_id = task_data.get('fileid', '')
                        
                        print(f"导出进度: {percent}% - {task_data.get('statuscol', '')}")
                        
                        return {
                            'status': status,
                            'percent': percent,
                            'task_id': task_id,
                            'file_id': file_id,
                            'completed': status == 1
                        }
                return None
            except Exception as e:
                print(f"解析导出状态失败: {e}")
                return None
        return None

    def update_export_status(self, report_id=216, task_id=16808, status=2):
        """
        步骤4: 更新导出任务状态
        """
        url = f"{self.base_url}/api/workflow/standCustomReport/exportTask"
        
        params = {
            'reportid': str(report_id),
            'method': 'update',
            'id': str(task_id),
            'status': str(status),
            '__random__': str(int(time.time() * 1000))
        }
        
        response = self.session.get(url, params=params)
        print(f"更新任务状态: {response.status_code}")
        return response.status_code == 200

    def download_file(self, file_id, output_path="downloaded_report.xlsx"):
        """
        步骤5: 下载生成的Excel文件
        """
        url = f"{self.base_url}/weaver/weaver.file.FileDownload"
        
        params = {
            'fileid': file_id
        }
        
        # 下载时需要不同的headers
        download_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Upgrade-Insecure-Requests': '1'
        }
        
        response = self.session.get(url, params=params, headers=download_headers, stream=True)
        print(f"下载文件: {response.status_code}")
        
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"文件已保存到: {output_path}")
            return True
        return False

    def get_table_data(self, data_key, current_page=1):
        """
        获取表格数据详情
        """
        url = f"{self.base_url}/api/ec/dev/table/datas"
        
        data = {
            'dataKey': data_key,
            'current': str(current_page),
            'sortParams': '[]'
        }
        
        response = self.session.post(url, data=data)
        print(f"获取表格数据: {response.status_code}")
        
        if response.status_code == 200:
            return response.json()
        return None

    def get_table_count(self, data_key):
        """
        获取表格数据总数
        """
        url = f"{self.base_url}/api/ec/dev/table/counts"
        
        data = {
            'dataKey': data_key
        }
        
        response = self.session.post(url, data=data)
        print(f"获取数据总数: {response.status_code}")
        
        if response.status_code == 200:
            return response.json()
        return None

    def full_download_process(self, report_id=216, field_conditions=None, filename="预付款申请单（2023）"):
        """
        完整的下载流程
        """
        print("=== 开始Excel报表下载流程 ===")
        
        # 步骤1: 获取报表数据
        print("\n1. 获取报表数据...")
        data_key = self.get_report_data(report_id, field_conditions)
        if not data_key:
            print("❌ 获取报表数据失败")
            return False
        
        print(f"✅ 获取到数据键: {data_key}")
        
        # 步骤2: 请求导出Excel（异步）
        print("\n2. 请求导出Excel...")
        export_success = self.export_excel(report_id, data_key, filename)
        if not export_success:
            print("❌ 导出请求失败")
            return False
        
        print("✅ 异步导出任务已启动")
        
        # 步骤3: 轮询检查导出状态
        print("\n3. 等待导出完成...")
        max_retries = 30  # 增加重试次数
        task_id = None
        file_id = None
        
        for i in range(max_retries):
            status_info = self.check_export_status(report_id)
            
            if status_info:
                if status_info['completed']:
                    file_id = status_info['file_id']
                    task_id = status_info['task_id']
                    print(f"✅ 导出完成！文件ID: {file_id}")
                    break
                else:
                    print(f"⏳ 导出进度: {status_info['percent']}%")
            else:
                print(f"⚠️ 无法获取导出状态")
            
            if i < max_retries - 1:  # 最后一次不要等待
                time.sleep(2)
        else:
            print("❌ 导出超时")
            return False
        
        # 步骤4: 更新任务状态（可选）
        if task_id:
            print("\n4. 更新任务状态...")
            self.update_export_status(report_id, task_id, 2)
        
        # 步骤5: 下载文件
        if file_id:
            print("\n5. 下载文件...")
            timestamp = int(time.time())
            output_path = f"{filename}-{timestamp}.xlsx"
            if self.download_file(file_id, output_path):
                print(f"✅ 下载完成: {output_path}")
                return True
            else:
                print("❌ 文件下载失败")
                return False
        else:
            print("❌ 未获取到文件ID")
            return False

    def _extract_file_id(self, status_result):
        """从状态响应中提取文件ID"""
        # 需要根据实际API响应结构来实现
        # 示例中的文件ID: a8544e62fce74eee3c561fbfaeda80f745593dbc94e1cde12ab3aa5c74c1203f7af063fcb08aacc84745b00a6acc7c83e071f8f8c08a1ba2d
        if isinstance(status_result, dict):
            # 检查各种可能的字段名
            for key in ['fileId', 'fileid', 'file_id', 'downloadUrl', 'url']:
                if key in status_result:
                    return status_result[key]
        return None


# 使用示例
if __name__ == "__main__":
    downloader = OAExcelDownloader()
    
    # 复现 cURL 中 "未设置条件" 时的筛选逻辑
    custom_conditions_from_curl = {
        'field-12_value1': '1107',
        'conditionfieldids': '-12',
        'templateid': '0',
        # reportid 和 reportParamsKey 可以让函数内部根据 report_id 参数默认生成
        # 如果需要精确匹配 cURL 中的 reportParamsKey，你需要确保时间戳部分也一致，但这通常是动态的
    }
    # success = downloader.full_download_process(field_conditions=custom_conditions_from_curl)
    
    # 或者，如果只是想简单地使用脚本默认的 report_id=216，并应用上述 curl 的筛选：
    minimal_custom_conditions = {
        'field-12_value1': '1107',
        'conditionfieldids': '-12',
        'templateid': '0' 
        # 'reportid': '216', # full_download_process 默认 report_id 就是 216
        # 'reportParamsKey': f'216{int(time.time() * 1000)}' # 会在 get_report_data 中自动生成
    }
    success = downloader.full_download_process(field_conditions=minimal_custom_conditions)

    # 原来的调用方式（使用脚本内部的默认双重筛选）
    # success = downloader.full_download_process() 